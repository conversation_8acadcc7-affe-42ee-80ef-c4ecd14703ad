<!-- 设备统计 -->
<template>
  <CPanel>
    <template #header>设备统计</template>
    <template #content>
      <div class="equipment-stats">
        <!-- 设备类型统计 -->
        <div class="equipment-types">
          <div class="equipment-item">
            <div class="equipment-icon">
              <img src="@/assets/img/水泵icon.png" alt="地理侧循环泵" />
            </div>
            <div class="equipment-info">
              <div class="equipment-name">地理侧循环泵</div>
              <div class="equipment-count" :style="{ backgroundImage: 'url(' + dyTitleImg + ')' }">
                14个
              </div>
            </div>
          </div>

          <div class="equipment-item">
            <div class="equipment-icon">
              <img src="@/assets/img/水泵icon.png" alt="补水泵" />
            </div>
            <div class="equipment-info">
              <div class="equipment-name">补水泵</div>
              <div class="equipment-count" :style="{ backgroundImage: 'url(' + dyTitleImg + ')' }">
                21个
              </div>
            </div>
          </div>

          <div class="equipment-item">
            <div class="equipment-icon">
              <img src="@/assets/img/水泵icon.png" alt="机组" />
            </div>
            <div class="equipment-info">
              <div class="equipment-name">机组</div>
              <div class="equipment-count" :style="{ backgroundImage: 'url(' + dyTitleImg + ')' }">
                12个
              </div>
            </div>
          </div>
        </div>

        <!-- 设备状态标题 -->
        <div class="status-title" :style="{ backgroundImage: 'url(' + nSmallTitleImg + ')' }">
          <span class="diamond">◆</span> 设备状态
        </div>

        <!-- 设备状态统计 -->
        <div class="equipment-status">
          <!-- 左侧统计 -->
          <div class="status-left">
            <div class="status-item" :style="{ backgroundImage: 'url(' + sbLeftImg + ')' }">
              <div class="status-label">设备总数</div>
              <div class="status-value">124</div>
            </div>
            <div class="status-item" :style="{ backgroundImage: 'url(' + sbLeftImg + ')' }">
              <div class="status-label">在线</div>
              <div class="status-value">12</div>
            </div>
          </div>

          <!-- 中间图片 -->
          <div class="status-center">
            <img src="@/assets/img/sb_center.png" alt="设备中心图" />
          </div>

          <!-- 右侧统计 -->
          <div class="status-right">
            <div class="status-item" :style="{ backgroundImage: 'url(' + sbRightImg + ')' }">
              <div class="status-label">离线</div>
              <div class="status-value">4</div>
            </div>
            <div class="status-item" :style="{ backgroundImage: 'url(' + sbRightImg + ')' }">
              <div class="status-label">故障</div>
              <div class="status-value">23</div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import CPanel from '@/components/common/CPanel.vue'
import dyTitleImg from '@/assets/img/dy_title.png'
import nSmallTitleImg from '@/assets/img/n_samllTitle.png'
import sbLeftImg from '@/assets/img/sb_left.png'
import sbRightImg from '@/assets/img/sb_right.png'
</script>

<style lang="scss" scoped>
.equipment-stats {
  padding: 20px;
  color: #fff;
}

.equipment-types {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
  gap: 20px;
}

.equipment-item {
  display: flex;
  align-items: center;
  flex: 1;
}

.equipment-icon {
  margin-right: 15px;

  img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 2px solid rgba(147, 185, 255, 0.5);
    padding: 8px;
    background: rgba(147, 185, 255, 0.1);
  }
}

.equipment-info {
  flex: 1;
}

.equipment-name {
  font-size: 14px;
  color: #C5D6E6;
  margin-bottom: 8px;
}

.equipment-count {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  padding: 8px 15px;
  text-align: center;
  min-height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-title {
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  padding: 12px 20px;
  margin-bottom: 20px;
  font-size: 16px;
  color: #fff;
  display: flex;
  align-items: center;
  min-height: 40px;

  .diamond {
    color: #4ECDC4;
    margin-right: 8px;
    font-size: 12px;
  }
}

.equipment-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
}

.status-left,
.status-right {
  display: flex;
  flex-direction: column;
  gap: 15px;
  flex: 1;
}

.status-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    max-width: 120px;
    max-height: 120px;
  }
}

.status-item {
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  padding: 15px 20px;
  text-align: center;
  min-height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.status-label {
  font-size: 14px;
  color: #C5D6E6;
  margin-bottom: 5px;
}

.status-value {
  font-size: 24px;
  font-weight: bold;
  color: #fff;
}
</style>
